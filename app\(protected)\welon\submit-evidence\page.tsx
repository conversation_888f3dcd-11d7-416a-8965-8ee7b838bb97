'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Info } from 'lucide-react';
import { EvidenceUploadForm } from '@/components/emergency/evidence-upload-form';
import { useSearchParams } from 'next/navigation';
import { useEvidenceSubmissions } from '@/hooks/useEvidenceSubmissions';
import { Headline, Subhead } from '@workspace/ui/brand';

// Define the AccessTrigger type
type AccessTrigger = 'Death' | 'Incapacitation';

// Loading fallback component
function SubmitEvidencePageLoading() {
  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-3xl mx-auto'>
        <div className='mb-8'>
          <div className='h-8 w-64 bg-gray-200 rounded animate-pulse mb-2'></div>
          <div className='h-4 w-96 bg-gray-200 rounded animate-pulse'></div>
        </div>
        <div className='h-96 bg-gray-100 rounded-lg animate-pulse'></div>
      </div>
    </div>
  );
}

// Main component that uses useSearchParams
function SubmitEvidenceContent() {
  const searchParams = useSearchParams();
  const typeParam = searchParams.get('type') as AccessTrigger | null;
  const { createEvidenceSubmission } = useEvidenceSubmissions();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [evidenceType, setEvidenceType] = useState<AccessTrigger>(
    typeParam === 'Death' ? 'Death' : 'Incapacitation'
  );
  const [alert, setAlert] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);

  // Update evidence type when URL parameter changes
  useEffect(() => {
    if (typeParam === 'Death' || typeParam === 'Incapacitation') {
      setEvidenceType(typeParam);
    }
  }, [typeParam]);

  const handleSubmit = async (formData: {
    evidenceType: AccessTrigger;
    memberEmail: string;
    memberName: string;
    relationship: string;
    additionalInfo: string;
    file: File;
  }) => {
    setIsSubmitting(true);
    setAlert(null);

    try {
      // Create evidence submission record with file upload
      const submissionData = {
        evidenceType: formData.evidenceType,
        memberEmail: formData.memberEmail,
        memberName: formData.memberName,
        relationship: formData.relationship,
        additionalInfo: formData.additionalInfo || '',
        status: 'pending' as const,
        submitterName: formData.memberName, // For simplicity, using same name
        submitterEmail: formData.memberEmail, // For simplicity, using same email
        file: formData.file, // Pass the file for upload
      };

      console.log('Submitting evidence with file:', submissionData);

      // Use the hook to create the submission and upload the file
      await createEvidenceSubmission(submissionData);

      setAlert({
        type: 'success',
        message: `Your ${formData.evidenceType === 'Death' ? 'death certificate' : 'medical certificate'} has been uploaded and submitted successfully. Our team will review it and contact you shortly.`,
      });

      // Clear success message after 10 seconds
      setTimeout(() => setAlert(null), 10000);
    } catch (error) {
      console.error('Error submitting evidence:', error);
      setAlert({
        type: 'error',
        message:
          'There was an error submitting your evidence. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-3xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>
            {evidenceType === 'Death'
              ? 'Submit Death Certificate'
              : 'Submit Incapacitation Evidence'}
          </Headline>
          <Subhead className='text-muted-foreground'>
            {evidenceType === 'Death'
              ? "Upload a death certificate to verify a member's passing and access their documents."
              : "Upload medical documentation to verify a member's incapacitation and access their documents."}
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : alert.type === 'error'
                  ? 'bg-destructive/10 text-destructive border-destructive/20'
                  : 'bg-blue-50 text-blue-800 border-blue-200'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : alert.type === 'error' ? (
              <AlertCircle className='h-4 w-4' />
            ) : (
              <Info className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success'
                ? 'Success'
                : alert.type === 'error'
                  ? 'Error'
                  : 'Information'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        <EvidenceUploadForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          error={alert?.type === 'error' ? alert.message : undefined}
          success={alert?.type === 'success' ? alert.message : undefined}
          initialEvidenceType={evidenceType}
        />
      </div>
    </div>
  );
}

// Export the main component wrapped in Suspense
export default function SubmitEvidencePage() {
  return (
    <Suspense fallback={<SubmitEvidencePageLoading />}>
      <SubmitEvidenceContent />
    </Suspense>
  );
}
