'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  fetchUserByCognitoId,
  fetchUserById,
  getAssignedToUserWelonTrustCognitoId,
} from '@/lib/data/users';
import { createNotification } from '@/lib/api/notifications';

// Generate the Amplify data client
const client = generateClient<Schema>();

// Use auto-generated Document type from Amplify schema
type DocumentType = Schema['Document']['type'];

// Types for document creation - based on the schema but with optional fields for creation
export interface CreateDocumentData {
  title: string;
  type: DocumentType['type'];
  status?: DocumentType['status'];
  version: string;
  content: string; // Populated template content
  fileUrl?: string;
  trackingNumber?: string;
  signatureType?: DocumentType['signatureType'];
  executionDate?: string;
  templateId?: string;
  templateContent?: string;
  documentState?: string;
  assignedWelonTrustId?: string;
}

export interface DocumentResponse {
  id: string;
  title: string;
  type: string;
  status: string;
  dateCreated: string;
  lastModified?: string;
  version: string;
  userId: string;
  content: string;
  fileUrl?: string;
  trackingNumber?: string;
  signatureType?: string;
  executionDate?: string;
  createdAt?: string;
  updatedAt?: string;
  rejectionReason?: string;
}

/**
 * Create a new document in the database
 */
export async function createDocument(
  documentData: CreateDocumentData,
  userId: string | null
): Promise<DocumentResponse> {
  try {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const assignedWelonTrustId = await getAssignedToUserWelonTrustCognitoId(
      userId
    );

    const now = new Date().toISOString();

    console.log('===> SAVING DOCUMENT FOR USER WITH ID', userId);

    // Create the document
    const { data, errors } = await client.models.Document.create({
      title: documentData.title,
      type: documentData.type,
      status: documentData.status || 'draft',
      dateCreated: now,
      lastModified: now,
      version: documentData.version,
      content: documentData.content,
      userId: userId,
      fileUrl: documentData.fileUrl || null,
      trackingNumber: documentData.trackingNumber || null,
      signatureType: documentData.signatureType || null,
      assignedWelonTrustId: assignedWelonTrustId,
      executionDate: documentData.executionDate
        ? new Date(documentData.executionDate).toISOString()
        : null,
      createdAt: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors creating document:', errors);
      throw new Error(
        'Failed to create document: ' + errors.map(e => e.message).join(', ')
      );
    }

    if (!data) {
      throw new Error('Failed to create document: No data returned');
    }

    // Log the creation in DocumentUpdateLog
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: data.id!,
        userId: userId,
        changeType: 'Created',
        changeDescription: `Document "${documentData.title}" created`,
        previousVersion: 0,
        newVersion: 1,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document creation:', logError);
      // Continue even if logging fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error creating document:', error);
    throw error;
  }
}

/**
 * Get all documents for the current user
 */
export async function getUserDocuments(): Promise<DocumentResponse[]> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const currentUser = await fetchUserByCognitoId(user.userId);

    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const { data, errors } = await client.models.Document.list({
      filter: {
        userId: {
          eq: currentUser?.id,
        },
      },
    });

    if (errors) {
      console.error('Errors fetching documents:', errors);
      throw new Error('Failed to fetch documents');
    }

    return data.map(doc => ({
      id: doc.id!,
      title: doc.title,
      type: doc.type!,
      status: doc.status!,
      dateCreated: doc.dateCreated,
      lastModified: doc.lastModified || undefined,
      version: doc.version,
      content: doc.content,
      userId: doc.userId,
      fileUrl: doc.fileUrl || undefined,
      trackingNumber: doc.trackingNumber || undefined,
      signatureType: doc.signatureType || undefined,
      executionDate: doc.executionDate || undefined,
      createdAt: doc.createdAt || undefined,
      updatedAt: doc.updatedAt || undefined,
      rejectionReason: doc.rejectionReason || undefined,
    }));
  } catch (error) {
    console.error('Error fetching user documents:', error);
    throw error;
  }
}

/**
 * Get documents for Welon Trust users - documents from assigned users with status "signed"
 */
export async function getWelonTrustDocuments(): Promise<DocumentResponse[]> {
  try {
    const user = await getCurrentUser();

    const currentUser = await fetchUserByCognitoId(user.userId);

    if (!currentUser) {
      return [];
    }

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    // First, get all users assigned to this Welon Trust user
    const { data: assignments, errors: assignmentErrors } =
      await client.models.WelonTrustAssignment.list({
        filter: {
          welonTrustUserId: {
            eq: currentUser.id,
          },
        },
      });

    if (assignmentErrors) {
      console.error(
        'Errors fetching Welon Trust assignments:',
        assignmentErrors
      );
      throw new Error('Failed to fetch assigned users');
    }

    if (!assignments || assignments.length === 0) {
      // No assigned users, return empty array
      return [];
    }

    // Extract user IDs from assignments
    const assignedUserIds = assignments.map(assignment => assignment.userId);

    // Create OR filter for all assigned user IDs
    const userFilters = assignedUserIds.map(userId => ({
      userId: { eq: userId },
    }));

    // Fetch documents from assigned users with status "shipped" (sent to Welon for review)
    const { data, errors } = await client.models.Document.list({
      filter: {
        and: [
          {
            or: userFilters,
          },
          {
            or: [
              { status: { eq: 'shipped' } },
              { status: { eq: 'received' } },
              { status: { eq: 'approved' } },
              { status: { eq: 'rejected' } },
            ],
          },
        ],
      },
    });

    if (errors) {
      console.error('Errors fetching Welon Trust documents:', errors);
      throw new Error('Failed to fetch documents for assigned users');
    }

    return data.map(doc => ({
      id: doc.id!,
      title: doc.title,
      type: doc.type!,
      status: doc.status!,
      dateCreated: doc.dateCreated,
      lastModified: doc.lastModified || undefined,
      version: doc.version,
      content: doc.content,
      userId: doc.userId,
      fileUrl: doc.fileUrl || undefined,
      trackingNumber: doc.trackingNumber || undefined,
      signatureType: doc.signatureType || undefined,
      executionDate: doc.executionDate || undefined,
      createdAt: doc.createdAt || undefined,
      updatedAt: doc.updatedAt || undefined,
      rejectionReason: doc.rejectionReason || undefined,
    }));
  } catch (error) {
    console.error('Error fetching Welon Trust documents:', error);
    throw error;
  }
}

/**
 * Get documents for a specific user (for Welon Trust users viewing selected user's documents)
 */
export async function getDocumentsForSelectedUser(
  selectedUserId: string
): Promise<DocumentResponse[]> {
  try {
    const user = await getCurrentUser();

    const currentUser = await fetchUserByCognitoId(user.userId);

    if (!currentUser) {
      return [];
    }

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    // Fetch documents from the selected user with status "shipped"
    const { data, errors } = await client.models.Document.list({
      filter: {
        and: [
          { userId: { eq: selectedUserId } },
          { status: { eq: 'shipped' } },
        ],
      },
    });

    if (errors) {
      console.error('Errors fetching documents for selected user:', errors);
      throw new Error('Failed to fetch documents for selected user');
    }

    return data.map(doc => ({
      id: doc.id!,
      title: doc.title,
      type: doc.type!,
      status: doc.status!,
      dateCreated: doc.dateCreated,
      lastModified: doc.lastModified || undefined,
      version: doc.version,
      content: doc.content,
      userId: doc.userId,
      fileUrl: doc.fileUrl || undefined,
      trackingNumber: doc.trackingNumber || undefined,
      signatureType: doc.signatureType || undefined,
      executionDate: doc.executionDate || undefined,
      createdAt: doc.createdAt || undefined,
      updatedAt: doc.updatedAt || undefined,
      rejectionReason: doc.rejectionReason || undefined,
    }));
  } catch (error) {
    console.error('Error fetching documents for selected user:', error);
    throw error;
  }
}

/**
 * Update a document
 */
export async function updateDocument(
  documentId: string,
  updates: Partial<CreateDocumentData>
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      ...updates,
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors updating document:', errors);
      throw new Error('Failed to update document');
    }

    if (!data) {
      throw new Error('Failed to update document: No data returned');
    }

    // Log the update
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: documentId,
        userId: user.userId,
        changeType: 'Updated',
        changeDescription: `Document "${data.title}" updated`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document update:', logError);
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error updating document:', error);
    throw error;
  }
}

/**
 * Get a single document by ID
 */
export async function getDocumentById(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const { data, errors } = await client.models.Document.get({
      id: documentId,
    });

    console.log('===> DOCUMENT REVIEW DATA', data);

    if (errors) {
      console.error('Errors fetching document:', errors);
      throw new Error('Failed to fetch document');
    }

    if (!data) {
      throw new Error('Document not found');
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      content: data.content,
      version: data.version,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error fetching document by ID:', error);
    throw error;
  }
}

/**
 * Mark a document as signed
 */
export async function signDocument(
  documentId: string,
  signatureType: 'manual' | 'electronic' | 'notarized' = 'electronic'
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'signed',
      signatureType: signatureType,
      executionDate: now,
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors signing document:', errors);
      throw new Error('Failed to sign document');
    }

    if (!data) {
      throw new Error('Failed to sign document: No data returned');
    }

    // Log the signing
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: documentId,
        userId: user.userId,
        changeType: 'Updated',
        changeDescription: `Document "${data.title}" signed with ${signatureType} signature`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document signing:', logError);
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error signing document:', error);
    throw error;
  }
}

// FUNCTION THAT MARK DOCUMENT AS SHIPPED
export const sendDocumentToWelon = async (documentId: string) => {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'shipped',
      lastModified: now,
      updatedAt: now,
    });
  } catch (error) {
    console.error('Error sending document to Welon:', error);
    throw error;
  }
};

/**
 * Mark a document as received by Welon Trust
 */
export async function markDocumentAsReceived(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'received',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors marking document as received:', errors);
      throw new Error('Failed to mark document as received');
    }

    if (!data) {
      throw new Error('Failed to mark document as received: No data returned');
    }

    // Log the action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: 'Document marked as received by Welon Trust',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document received:', logError);
      // Continue even if logging fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error marking document as received:', error);
    throw error;
  }
}

/**
 * Approve a document (Welon Trust action)
 */
export async function approveDocument(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'approved',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors approving document:', errors);
      throw new Error('Failed to approve document');
    }

    if (!data) {
      throw new Error('Failed to approve document: No data returned');
    }

    // Log the approval action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: 'Document approved',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document approval:', logError);
      // Continue even if logging fails
    }

    // Send notification to document owner
    try {
      // Get the user by database ID for notification
      const documentOwner = await fetchUserById(data.userId);
      if (documentOwner) {
        const notificationMessage = `Your document "${data.title}" has been approved by Welon Trust!`;
        await createNotification(notificationMessage, documentOwner.cognitoId);
      }
    } catch (notificationError) {
      console.error('Error creating approval notification:', notificationError);
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error approving document:', error);
    throw error;
  }
}

/**
 * Reject a document (Welon Trust action)
 */
export async function rejectDocument(
  documentId: string,
  rejectionReason: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    if (!rejectionReason.trim()) {
      throw new Error('Rejection reason is required');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'rejected',
      rejectionReason: rejectionReason.trim(),
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors rejecting document:', errors);
      throw new Error('Failed to reject document');
    }

    if (!data) {
      throw new Error('Failed to reject document: No data returned');
    }

    // Log the rejection action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: `Document rejected. Reason: ${rejectionReason}`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document rejection:', logError);
      // Continue even if logging fails
    }

    // Send notification to document owner
    try {
      // Get the user by database ID for notification
      const documentOwner = await fetchUserById(data.userId);
      if (documentOwner) {
        const notificationMessage = `Your document "${data.title}" has been rejected by Welon Trust. Reason: ${rejectionReason}`;
        await createNotification(notificationMessage, documentOwner.cognitoId);
      }
    } catch (notificationError) {
      console.error(
        'Error creating rejection notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error rejecting document:', error);
    throw error;
  }
}

/**
 * Resubmit a rejected document (change status from rejected to signed)
 */
export async function resubmitDocument(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'signed',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors resubmitting document:', errors);
      throw new Error('Failed to resubmit document');
    }

    if (!data) {
      throw new Error('Failed to resubmit document: No data returned');
    }

    // Log the resubmission action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Updated',
        changeDescription: 'Document resubmitted after rejection',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error creating resubmit log:', logError);
      // Continue even if logging fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
      rejectionReason: data.rejectionReason || undefined,
    };
  } catch (error) {
    console.error('Error resubmitting document:', error);
    throw error;
  }
}

/**
 * Delete (archive) a document
 */
export async function deleteDocument(documentId: string): Promise<void> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'archived',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors archiving document:', errors);
      throw new Error('Failed to archive document');
    }

    // Log the archival
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: documentId,
        userId: user.userId,
        changeType: 'Archived',
        changeDescription: 'Document archived',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document archival:', logError);
    }
  } catch (error) {
    console.error('Error archiving document:', error);
    throw error;
  }
}

/**
 * Assign a Welon Trust user to all documents of a specific user
 */
export const assignWelonToUserDocuments = async (
  userId: string,
  welonTrustCognitoId: string
): Promise<{ success: boolean; updatedCount: number; errors: string[] }> => {
  try {
    console.log('===> ASIGN WELON TO DOCUMENTS START');

    const errors: string[] = [];
    let updatedCount = 0;

    // First, get all documents for the specified user
    const { data: documents, errors: fetchErrors } =
      await client.models.Document.list({
        filter: {
          userId: {
            eq: userId,
          },
        },
      });

    console.log('===> ASIGN WELON TO DOCUMENTS USER DOCUMENTS LIST', documents);

    if (fetchErrors) {
      console.error('Errors fetching user documents:', fetchErrors);
      return {
        success: false,
        updatedCount: 0,
        errors: ['Failed to fetch user documents'],
      };
    }

    if (!documents || documents.length === 0) {
      return {
        success: true,
        updatedCount: 0,
        errors: [],
      };
    }

    // Update each document with the assigned Welon Trust ID
    const updatePromises = documents.map(async document => {
      try {
        const { data, errors: updateErrors } =
          await client.models.Document.update({
            id: document.id,
            assignedWelonTrustId: welonTrustCognitoId,
            lastModified: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

        if (updateErrors) {
          console.error(
            `Error updating document ${document.id}:`,
            updateErrors
          );
          errors.push(
            `Failed to update document "${document.title}": ${updateErrors
              .map(e => e.message)
              .join(', ')}`
          );
          return false;
        }

        if (data) {
          updatedCount++;
          return true;
        }
        return false;
      } catch (error) {
        console.error(`Error updating document ${document.id}:`, error);
        errors.push(
          `Failed to update document "${document.title}": ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
        return false;
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    return {
      success: errors.length === 0,
      updatedCount,
      errors,
    };
  } catch (error) {
    console.error('Error in assignWelonToUserDocuments:', error);
    return {
      success: false,
      updatedCount: 0,
      errors: [
        error instanceof Error ? error.message : 'Unknown error occurred',
      ],
    };
  }
};

/**
 * Remove Welon Trust assignment from all documents of a specific user
 */
export const removeWelonTrustFromUserDocuments = async (
  userId: string
): Promise<{ success: boolean; updatedCount: number; errors: string[] }> => {
  try {
    const errors: string[] = [];
    let updatedCount = 0;

    // Get all documents for the specified user that have an assigned Welon Trust
    const { data: documents, errors: fetchErrors } =
      await client.models.Document.list({
        filter: { userId: { eq: userId } },
      });

    if (fetchErrors) {
      console.error('Errors fetching user documents:', fetchErrors);
      return {
        success: false,
        updatedCount: 0,
        errors: ['Failed to fetch user documents'],
      };
    }

    if (!documents || documents.length === 0) {
      return {
        success: true,
        updatedCount: 0,
        errors: [],
      };
    }

    // Remove Welon Trust assignment from each document
    const updatePromises = documents.map(async document => {
      try {
        const { data, errors: updateErrors } =
          await client.models.Document.update({
            id: document.id,
            assignedWelonTrustId: null,
          });

        if (updateErrors) {
          console.error(
            `Error updating document ${document.id}:`,
            updateErrors
          );
          errors.push(
            `Failed to update document "${document.title}": ${updateErrors
              .map(e => e.message)
              .join(', ')}`
          );
          return false;
        }

        if (data) {
          updatedCount++;
          return true;
        }
        return false;
      } catch (error) {
        console.error(`Error updating document ${document.id}:`, error);
        errors.push(
          `Failed to update document "${document.title}": ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
        return false;
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    return {
      success: errors.length === 0,
      updatedCount,
      errors,
    };
  } catch (error) {
    console.error('Error in removeWelonTrustFromUserDocuments:', error);
    return {
      success: false,
      updatedCount: 0,
      errors: [
        error instanceof Error ? error.message : 'Unknown error occurred',
      ],
    };
  }
};

/**
 * Delete all documents for current user
 */
export async function deleteAllUserDocuments(): Promise<void> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    // Get all documents for the current user
    const { data: documents, errors } = await client.models.Document.list({
      filter: { userId: { eq: user.userId } },
    });

    if (errors) {
      throw new Error(errors[0].message);
    }

    // Delete each document
    for (const document of documents) {
      await client.models.Document.delete({ id: document.id });
    }

    console.log(
      `Deleted ${documents.length} documents for user ${user.userId}`
    );
  } catch (error) {
    console.error('Error deleting all user documents:', error);
    throw error;
  }
}
