'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Link from 'next/link';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useQuery } from '@tanstack/react-query';
import { fetchTemplates } from '@/app/utils/templates';

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

interface TemplateWithVersion extends Template {
  latestVersion?: TemplateVersion;
}

const Templates = () => {
  const [selectedState, setSelectedState] = useState('All States');
  const [selectedType, setSelectedType] = useState('All Types');
  const [sortField, setSortField] = useState<keyof Template>('templateState');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(
    'asc'
  );
  const [activeTab, setActiveTab] = useState('active');
  const [selectedTemplate, setSelectedTemplate] =
    useState<TemplateWithVersion | null>(null);
  const [isVersionsModalOpen, setIsVersionsModalOpen] = useState(false);
  const [templateVersions, setTemplateVersions] = useState<TemplateVersion[]>(
    []
  );
  const client = generateClient<Schema>();

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['templates'],
    queryFn: fetchTemplates,
  });

  const toggleTemplateStatus = async (template: TemplateWithVersion) => {
    try {
      await client.models.Template.update({
        id: template.id,
        isActive: !template.isActive,
      });
      toast.success(
        `Template ${template.isActive ? 'archived' : 'unarchived'} successfully`
      );
      // Refetch templates to update the list
      await fetchTemplates();
    } catch (error) {
      console.error('Error updating template status:', error);
      toast.error('Failed to update template status');
    }
  };

  const fetchTemplateVersions = async (templateId: string) => {
    try {
      const { data: versions } = await client.models.TemplateVersion.list({
        filter: {
          templateId: { eq: templateId },
        },
      });
      setTemplateVersions(
        versions.sort((a, b) => b.versionNumber - a.versionNumber)
      );
    } catch (error) {
      console.error('Error fetching template versions:', error);
      toast.error('Failed to fetch template versions');
    }
  };

  const handleSort = (field: keyof Template) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleViewVersionsClick = async (template: TemplateWithVersion) => {
    setSelectedTemplate(template);
    await fetchTemplateVersions(template.id);
    setIsVersionsModalOpen(true);
  };

  const filteredTemplates = templates.filter(template => {
    const stateMatch =
      selectedState === 'All States' ||
      template.templateState === selectedState;
    const typeMatch =
      selectedType === 'All Types' || template.type === selectedType;
    const activeMatch =
      activeTab === 'active' ? template.isActive : !template.isActive;
    return stateMatch && typeMatch && activeMatch;
  });

  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    if (!sortDirection) return 0;
    const multiplier = sortDirection === 'asc' ? 1 : -1;
    const aValue = a[sortField];
    const bValue = b[sortField];
    return String(aValue).localeCompare(String(bValue)) * multiplier;
  });

  // Get unique states and types from templates
  const states = [
    'All States',
    ...new Set(templates.map(t => t.templateState)),
  ];
  const documentTypes = ['All Types', ...new Set(templates.map(t => t.type))];

  return (
    <div className='p-6'>
      <div className='flex justify-between items-center mb-6'>
        <h1 className='text-2xl font-bold'>Template Management</h1>
        {!isLoading && (
          <Link href='/admin/templates/editor'>
            <Button>Create New Template</Button>
          </Link>
        )}
      </div>

      <Tabs
        defaultValue='active'
        className='w-full'
        onValueChange={setActiveTab}
      >
        <TabsList className='mb-6'>
          <TabsTrigger value='active'>Active Templates</TabsTrigger>
          <TabsTrigger value='archived'>Archived Templates</TabsTrigger>
        </TabsList>

        <TabsContent value='active'>
          <div className='flex gap-4 mb-6'>
            <Select value={selectedState} onValueChange={setSelectedState}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Select State' />
              </SelectTrigger>
              <SelectContent>
                {states.map(state => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Select Type' />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className='cursor-pointer'
                    onClick={() => handleSort('templateState')}
                  >
                    State{' '}
                    {sortField === 'templateState' &&
                      (sortDirection === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead
                    className='cursor-pointer'
                    onClick={() => handleSort('type')}
                  >
                    Type{' '}
                    {sortField === 'type' &&
                      (sortDirection === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Template Name</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center'>
                      Loading templates...
                    </TableCell>
                  </TableRow>
                ) : sortedTemplates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center'>
                      No templates found
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedTemplates.map(template => (
                    <TableRow key={template.id}>
                      <TableCell>{template.templateState}</TableCell>
                      <TableCell>{template.type}</TableCell>
                      <TableCell>
                        {template.latestVersion?.versionNumber || 'N/A'}
                      </TableCell>
                      <TableCell>{template.templateName}</TableCell>
                      <TableCell>
                        {new Date(template.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(template.updatedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{template.createdBy}</TableCell>
                      <TableCell>
                        <div className='flex gap-2'>
                          <Link href={`/admin/templates/${template.id}`}>
                            <Button variant='outline' size='sm'>
                              Update
                            </Button>
                          </Link>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleViewVersionsClick(template)}
                          >
                            View Versions
                          </Button>
                          <Button
                            variant={
                              template.isActive ? 'destructive' : 'default'
                            }
                            size='sm'
                            onClick={() => toggleTemplateStatus(template)}
                          >
                            {template.isActive ? 'Archive' : 'Unarchive'}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value='archived'>
          <div className='flex gap-4 mb-6'>
            <Select value={selectedState} onValueChange={setSelectedState}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Select State' />
              </SelectTrigger>
              <SelectContent>
                {states.map(state => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Select Type' />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className='cursor-pointer'
                    onClick={() => handleSort('templateState')}
                  >
                    State{' '}
                    {sortField === 'templateState' &&
                      (sortDirection === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead
                    className='cursor-pointer'
                    onClick={() => handleSort('type')}
                  >
                    Type{' '}
                    {sortField === 'type' &&
                      (sortDirection === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Template Name</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center'>
                      Loading templates...
                    </TableCell>
                  </TableRow>
                ) : sortedTemplates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center'>
                      No archived templates found
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedTemplates.map(template => (
                    <TableRow key={template.id}>
                      <TableCell>{template.templateState}</TableCell>
                      <TableCell>{template.type}</TableCell>
                      <TableCell>
                        {template.latestVersion?.versionNumber || 'N/A'}
                      </TableCell>
                      <TableCell>{template.templateName}</TableCell>
                      <TableCell>
                        {new Date(template.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(template.updatedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{template.createdBy}</TableCell>
                      <TableCell>
                        <div className='flex gap-2'>
                          <Link href={`/admin/templates/${template.id}`}>
                            <Button variant='outline' size='sm'>
                              Update
                            </Button>
                          </Link>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleViewVersionsClick(template)}
                          >
                            View Versions
                          </Button>
                          <Button
                            variant={
                              template.isActive ? 'destructive' : 'default'
                            }
                            size='sm'
                            onClick={() => toggleTemplateStatus(template)}
                          >
                            {template.isActive ? 'Archive' : 'Unarchive'}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      <Dialog open={isVersionsModalOpen} onOpenChange={setIsVersionsModalOpen}>
        <DialogContent className='max-w-4xl'>
          <DialogHeader>
            <DialogTitle>
              Template Versions - {selectedTemplate?.templateName}
            </DialogTitle>
          </DialogHeader>
          <div className='space-y-4'>
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Version</TableHead>
                    <TableHead>Created At</TableHead>
                    <TableHead>Content</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {templateVersions.map(version => (
                    <TableRow
                      key={`${version.templateId}-${version.versionNumber}`}
                    >
                      <TableCell>v{version.versionNumber}</TableCell>
                      <TableCell>
                        {new Date(version.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <ScrollArea className='h-[100px] w-full rounded-md border p-4'>
                          <pre className='whitespace-pre-wrap text-sm'>
                            {version.content}
                          </pre>
                        </ScrollArea>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className='flex justify-end'>
              <Button
                variant='outline'
                onClick={() => setIsVersionsModalOpen(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Templates;
