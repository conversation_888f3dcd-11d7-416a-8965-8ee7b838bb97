import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { Schema } from '@/amplify/data/resource';
import { toast } from 'sonner';
import { uploadData, getUrl, remove } from 'aws-amplify/storage';
import { useQuery } from '@tanstack/react-query';
import { getCurrentUser } from 'aws-amplify/auth';

// Define the EvidenceSubmission type based on your schema
interface EvidenceSubmission {
  id: string;
  submitterName: string;
  submitterEmail: string;
  memberName: string;
  memberEmail: string;
  relationship: string;
  evidenceType: 'Death' | 'Incapacitation';
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  additionalInfo?: string;
  fileKey?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
  reviewedBy?: string;
  reviewedAt?: string;
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export function useEvidenceSubmissions() {
  const [evidenceSubmissions, setEvidenceSubmissions] = useState<
    EvidenceSubmission[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const client = generateClient<Schema>();

  // Use React Query for fetching evidence submissions
  const { refetch } = useQuery({
    queryKey: ['evidenceSubmissions'],
    queryFn: async () => {
      console.log('Fetching evidence submissions from API...');
      setLoading(true);
      setError(null);

      try {
        const result = await client.models.EvidenceSubmission.list();

        if (result.data) {
          console.log('Successfully loaded evidence submissions:', result.data);
          setEvidenceSubmissions(result.data as EvidenceSubmission[]);
        } else {
          console.log('No evidence submissions found');
          setEvidenceSubmissions([]);
        }
        return result.data;
      } catch (err: any) {
        console.error('Error fetching evidence submissions:', err);
        const errorMsg = `Failed to load evidence submissions: ${err.message || 'Unknown error'}`;
        setError(errorMsg);
        throw new Error(errorMsg);
      } finally {
        setLoading(false);
      }
    },
    // Don't auto-fetch on mount since we'll use the useEffect below
    enabled: false,
  });

  // Expose the fetchEvidenceSubmissions function that uses React Query's refetch
  const fetchEvidenceSubmissions = async () => {
    return refetch();
  };

  // File upload functionality
  const uploadFile = async (
    file: File,
    submissionId: string
  ): Promise<{
    key: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    uploadedBy: string;
  }> => {
    try {
      let user;
      try {
        // Check if user is authenticated
        user = await getCurrentUser();
        console.log('Current user:', user);
      } catch (authError) {
        console.error('User not authenticated:', authError);
        throw new Error('You must be logged in to upload files');
      }

      console.log('Uploading file:', file.name);

      // Create a unique file key using the public/evidence path with owner-based access
      // The {entity_id} will be automatically replaced with the user's ID by Amplify
      const fileKey = `evidence/${user.userId}/${submissionId}/${file.name}`;

      // Upload the file to S3 using Amplify Storage
      const result = await uploadData({
        key: fileKey,
        data: file,
        options: {
          contentType: file.type,
          metadata: {
            submissionId,
            fileName: file.name,
            uploadedBy: user.userId,
          },
        },
      });

      console.log('File uploaded successfully:', result);

      return {
        key: fileKey,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        uploadedBy: user.userId,
      };
    } catch (err) {
      console.error('Error uploading file:', err);
      throw err;
    }
  };

  // Get a signed URL for a file
  const getFileUrl = async (fileKey: string): Promise<string> => {
    try {
      if (!fileKey) {
        throw new Error('File key is empty or undefined');
      }

      console.log('Getting URL for file key:', fileKey);

      const result = await getUrl({
        key: fileKey,
        options: {
          validateObjectExistence: true,
        },
      });

      console.log('Successfully retrieved URL:', result.url.toString());
      return result.url.toString();
    } catch (err) {
      console.error('Error getting file URL:', err, 'for key:', fileKey);
      toast.error(
        `Failed to load file: ${err instanceof Error ? err.message : 'Unknown error'}`
      );
      throw err;
    }
  };

  // Delete file from storage
  const deleteFile = async (fileKey: string): Promise<void> => {
    try {
      console.log('Deleting file:', fileKey);

      await remove({
        key: fileKey,
      });

      console.log('File deleted successfully');
    } catch (err: any) {
      console.error('Error deleting file:', err);
      throw new Error(
        `Failed to delete file: ${err.message || 'Unknown error'}`
      );
    }
  };

  // Create a new evidence submission with file upload
  const createEvidenceSubmission = async (
    data: Omit<EvidenceSubmission, 'id' | 'createdAt' | 'updatedAt'> & {
      file: File;
    }
  ) => {
    try {
      console.log('Creating new evidence submission with file:', data);

      // First create the submission record to get an ID
      const submissionData = {
        submitterName: data.submitterName,
        submitterEmail: data.submitterEmail,
        memberName: data.memberName,
        memberEmail: data.memberEmail,
        relationship: data.relationship,
        evidenceType: data.evidenceType,
        status: 'pending',
        additionalInfo: data.additionalInfo || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const result = await client.models.EvidenceSubmission.create({
        ...submissionData,
        status: 'pending' as
          | 'pending'
          | 'under_review'
          | 'approved'
          | 'rejected',
      });

      if (!result.data) {
        throw new Error('Failed to create evidence submission record');
      }

      const submissionId = result.data.id;
      console.log('Created submission record with ID:', submissionId);

      // Check if submissionId exists before uploading
      if (!submissionId) {
        throw new Error(
          'Failed to create submission: No submission ID returned'
        );
      }

      const fileInfo = await uploadFile(data.file, submissionId);

      // Update the submission record with file information
      const updateResult = await client.models.EvidenceSubmission.update({
        id: submissionId,
        fileKey: fileInfo.key,
        fileName: fileInfo.fileName,
        fileType: fileInfo.fileType,
        fileSize: fileInfo.fileSize,
        uploadedBy: fileInfo.uploadedBy,
        updatedAt: new Date().toISOString(),
      });

      // Log the update result to verify file information is saved correctly
      console.log('Updated submission with file info:', {
        id: submissionId,
        fileKey: fileInfo.key,
        fileName: fileInfo.fileName,
        fileType: fileInfo.fileType,
        fileSize: fileInfo.fileSize,
      });

      if (updateResult.data) {
        console.log(
          'Successfully updated evidence submission with file info:',
          updateResult.data
        );
        // Update local state with the new submission
        setEvidenceSubmissions(prev => [
          updateResult.data as EvidenceSubmission,
          ...prev,
        ]);
        toast.success('Evidence submission created successfully');
        return updateResult.data;
      } else {
        throw new Error(
          'Failed to update evidence submission with file information'
        );
      }
    } catch (err: any) {
      console.error('Error creating evidence submission with file:', err);
      toast.error(
        `Failed to create evidence submission: ${err.message || 'Unknown error'}`
      );
      throw err;
    }
  };

  // Update evidence status
  const updateEvidenceStatus = async (
    id: string,
    status: 'pending' | 'under_review' | 'approved' | 'rejected',
    notes?: string
  ) => {
    try {
      console.log(`Updating evidence submission ${id} to status: ${status}`);

      const updateData: any = {
        id,
        status,
        updatedAt: new Date().toISOString(),
      };

      if (status === 'approved' || status === 'rejected') {
        updateData.reviewedAt = new Date().toISOString();
        updateData.reviewedBy = 'Admin User'; // In a real app, get this from auth context
      }

      if (notes) {
        updateData.reviewNotes = notes;
      }

      const result = await client.models.EvidenceSubmission.update(updateData);

      if (result.data) {
        console.log('Successfully updated evidence submission:', result.data);
        // Update the local state
        setEvidenceSubmissions(prev =>
          prev.map(item => (item.id === id ? { ...item, ...updateData } : item))
        );
        toast.success(`Evidence submission ${status}`);
        return result.data;
      } else {
        throw new Error('Failed to update evidence submission');
      }
    } catch (err: any) {
      console.error('Error updating evidence submission:', err);
      toast.error(
        `Failed to update evidence status: ${err.message || 'Unknown error'}`
      );
      throw err;
    }
  };

  // Delete an evidence submission
  const deleteEvidenceSubmission = async (id: string) => {
    try {
      console.log(`Deleting evidence submission ${id}`);

      // First get the submission to retrieve the file key
      const submission = evidenceSubmissions.find(item => item.id === id);

      if (submission?.fileKey) {
        // Delete the associated file first
        await deleteFile(submission.fileKey);
      }

      const result = await client.models.EvidenceSubmission.delete({
        id,
      });

      if (result.data) {
        console.log('Successfully deleted evidence submission:', result.data);
        // Update local state by removing the deleted submission
        setEvidenceSubmissions(prev => prev.filter(item => item.id !== id));
        toast.success('Evidence submission deleted');
        return result.data;
      } else {
        throw new Error('Failed to delete evidence submission');
      }
    } catch (err: any) {
      console.error('Error deleting evidence submission:', err);
      toast.error(
        `Failed to delete evidence submission: ${err.message || 'Unknown error'}`
      );
      throw err;
    }
  };

  // Load evidence submissions on component mount
  useEffect(() => {
    fetchEvidenceSubmissions();
  }, []);

  return {
    evidenceSubmissions,
    loading,
    error,
    fetchEvidenceSubmissions,
    createEvidenceSubmission,
    updateEvidenceStatus,
    deleteEvidenceSubmission,
    getFileUrl,
    uploadFile,
    deleteFile,
  };
}
