import { a } from '@aws-amplify/backend';

/**
 * User management related models
 */

export const UserAnswer = a.customType({
  questionId: a.string().required(),
  answer: a.string(),
  answeredAt: a.datetime().required(),
});

export const UserInterviewProgressNew = a.customType({
  interviewVersionId: a.id().required(),
  isCompleted: a.boolean().required(),
  startedAt: a.datetime().required(),
  completedAt: a.datetime(),
  currentQuestionId: a.string(),
  answers: a.ref('UserAnswer').array(),
});

// User status enum
export const UserStatus = a.enum(['active', 'inactive', 'pending']);

// Master role enum
export const MasterRole = a.enum([
  'Member',
  'Administrator',
  'WelonTrust',
  'Professional',
]);

// Link type enum
export const LinkType = a.enum([
  'primary',
  'secondary',
  'delegate',
  'emergency',
]);

// Link status enum
export const LinkStatus = a.enum(['pending', 'active', 'revoked']);

// WelonTrustAssignment status enum
export const WelonTrustAssignmentStatus = a.enum([
  'active',
  'pending',
  'revoked',
]);

// Main User model
export const User = a
  .model({
    email: a.string().required(),
    firstName: a.string().required(),
    lastName: a.string().required(),
    phoneNumber: a.string().required(),
    birthdate: a.string().required(),
    state: a.string().required(),
    cognitoId: a.string().required(),
    role: MasterRole,
    subrole: a.string(),
    status: UserStatus,
    journeyStatus: a.string(),
    createdAt: a.datetime().required(),
    // Relationships
    linkedAccounts: a.hasMany('LinkedAccount', 'userId'),
    assignedWelonTrust: a.hasOne('WelonTrustAssignment', 'userId'),
    assignedWelonTrustId: a.string(),
    documents: a.hasMany('Document', 'userId'),
    interviewProgress: a.ref('UserInterviewProgressNew').array(),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId'),
    allow.ownerDefinedIn('assignedWelonTrustId'),
    allow.group('ADMINS'),
  ]);

// Welon Trust assignment model
export const WelonTrustAssignment = a
  .model({
    userId: a.id().required(),
    welonTrustUserId: a.string().required(),
    welonTrustName: a.string().required(),
    welonTrustEmail: a.string().required(),
    welonTrustCognitoId: a.string().required().default(''),
    assignedAt: a.datetime().required(),
    assignedBy: a.string().required(), // Admin user ID who made the assignment
    status: WelonTrustAssignmentStatus,
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('welonTrustCognitoId'),
    allow.group('ADMINS'),
  ]);

// Linked account model
export const LinkedAccount = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    linkedUserId: a.string().required(), // The ID of the user who is linked
    linkType: LinkType, // The type of link
    status: LinkStatus, // The status of the link
    permissions: a.string().array().required(), // Permissions granted to the linked user
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    expiresAt: a.datetime(), // Optional expiration date
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.owner()]);
