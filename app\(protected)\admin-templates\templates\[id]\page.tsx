'use client';

import { useState } from 'react';
import { TipTapEditorWithVariables } from '@/components/ui/tiptap-editor';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getTemplate } from '@/app/utils/templates';

// Mock variables for demonstration
const mockVariables = [
  { id: '1', label: "User's Full Name", value: '{{user.fullName}}' },
  { id: '2', label: "User's Email", value: '{{user.email}}' },
  { id: '3', label: 'Current Date', value: '{{date.current}}' },
  { id: '4', label: 'Document Type', value: '{{document.type}}' },
  { id: '5', label: 'State', value: '{{document.state}}' },
];

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

interface TemplateWithVersion extends Template {
  latestVersion?: TemplateVersion;
}

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const [content, setContent] = useState('');
  const [templateName, setTemplateName] = useState('');
  const [selectedState, setSelectedState] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const router = useRouter();
  const client = generateClient<Schema>();
  const queryClient = useQueryClient();

  // Fetch template data
  const { data: template, isLoading } = useQuery({
    queryKey: ['template', id],
    queryFn: async () => {
      const { template, latestVersion } = await getTemplate(id);

      setTemplateName(template.templateName);
      setSelectedState(template.templateState);
      setSelectedType(template.type);
      setContent(latestVersion?.content || '');

      return template;
    },
  });

  // Update template mutation
  const updateTemplateMutation = useMutation({
    mutationFn: async () => {
      if (!templateName || !selectedState || !selectedType || !content) {
        throw new Error('Please fill in all required fields');
      }

      const now = new Date().toISOString();

      // Update the template
      const { errors: templateErrors } = await client.models.Template.update({
        id: id,
        templateName,
        type: selectedType,
        templateState: selectedState,
        updatedAt: now,
      });

      if (templateErrors) {
        throw new Error('Failed to update template');
      }

      // Get current version number
      const { data: versions } = await client.models.TemplateVersion.list({
        filter: {
          templateId: { eq: id },
        },
      });
      const latestVersion = versions.sort(
        (a, b) => b.versionNumber - a.versionNumber
      )[0];
      const newVersionNumber = (latestVersion?.versionNumber || 0) + 1;

      // Create a new version
      const { errors: versionErrors } =
        await client.models.TemplateVersion.create({
          templateId: id,
          versionNumber: newVersionNumber,
          content: content,
          createdAt: now,
        });

      if (versionErrors) {
        throw new Error('Failed to create template version');
      }
    },
    onSuccess: () => {
      toast.success('Template updated successfully');
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      router.push('/admin/templates');
    },
    onError: error => {
      console.error('Error updating template:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update template'
      );
    },
  });

  const handleSave = () => {
    updateTemplateMutation.mutate();
  };

  const handleCancel = () => {
    router.push('/admin/templates');
  };

  if (isLoading) {
    return <div className='container mx-auto py-6'>Loading...</div>;
  }

  return (
    <div className='container mx-auto py-6 space-y-6'>
      <div className='flex justify-between items-center'>
        <h1 className='text-2xl font-bold'>Update Template</h1>
        <div className='space-x-2'>
          <Button variant='outline' onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={updateTemplateMutation.isPending}
          >
            {updateTemplateMutation.isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Template Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Template Name</label>
              <Input
                placeholder='Enter template name'
                value={templateName}
                onChange={e => setTemplateName(e.target.value)}
              />
            </div>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>State</label>
              <Select value={selectedState} onValueChange={setSelectedState}>
                <SelectTrigger>
                  <SelectValue placeholder='Select state' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='CA'>California</SelectItem>
                  <SelectItem value='NY'>New York</SelectItem>
                  <SelectItem value='TX'>Texas</SelectItem>
                  {/* Add more states as needed */}
                </SelectContent>
              </Select>
            </div>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Document Type</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder='Select type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='will'>Will</SelectItem>
                  <SelectItem value='trust'>Trust</SelectItem>
                  <SelectItem value='poa'>Power of Attorney</SelectItem>
                  {/* Add more types as needed */}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Template Content</CardTitle>
        </CardHeader>
        <CardContent>
          <TipTapEditorWithVariables
            content={content}
            onChange={setContent}
            variables={mockVariables}
            placeholder='Start writing your template...'
          />
        </CardContent>
      </Card>
    </div>
  );
}
