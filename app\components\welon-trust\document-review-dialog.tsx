'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { Document } from '@/types/documents';

interface DocumentReviewDialogProps {
  document: Document | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (documentId: string) => Promise<void>;
  onReject: (documentId: string, rejectionReason: string) => Promise<void>;
  isProcessing?: boolean;
}

export function DocumentReviewDialog({
  document,
  isOpen,
  onClose,
  onApprove,
  onReject,
  isProcessing = false,
}: DocumentReviewDialogProps) {
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  const handleClose = () => {
    if (!isProcessing) {
      setAction(null);
      setRejectionReason('');
      onClose();
    }
  };

  const handleApprove = async () => {
    if (!document) return;

    try {
      await onApprove(document.id);
      handleClose();
    } catch (error) {
      console.error('Error approving document:', error);
    }
  };

  const handleReject = async () => {
    if (!document || !rejectionReason.trim()) return;

    try {
      await onReject(document.id, rejectionReason.trim());
      handleClose();
    } catch (error) {
      console.error('Error rejecting document:', error);
    }
  };

  const handleActionSelect = (selectedAction: 'approve' | 'reject') => {
    setAction(selectedAction);
    // Clear fields when switching actions
    setRejectionReason('');
  };

  if (!document) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-blue-600" />
            Review Document
          </DialogTitle>
          <DialogDescription>
            Review and take action on "{document.title}" from member.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Document Info */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm space-y-1">
              <div><strong>Document:</strong> {document.title}</div>
              <div><strong>Type:</strong> {document.type}</div>
              <div><strong>Status:</strong> {document.status}</div>
              <div><strong>Created:</strong> {new Date(document.dateCreated).toLocaleDateString()}</div>
            </div>
          </div>

          {/* Action Selection */}
          {!action && (
            <div className="space-y-3">
              <Label className="text-base font-medium">Choose Action:</Label>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={() => handleActionSelect('approve')}
                  className="h-auto p-4 flex flex-col items-center gap-2 border-green-200 hover:bg-green-50"
                  disabled={isProcessing}
                >
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <span className="font-medium text-green-700">Approve</span>
                  <span className="text-xs text-green-600">Document meets requirements</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleActionSelect('reject')}
                  className="h-auto p-4 flex flex-col items-center gap-2 border-red-200 hover:bg-red-50"
                  disabled={isProcessing}
                >
                  <XCircle className="h-6 w-6 text-red-600" />
                  <span className="font-medium text-red-700">Reject</span>
                  <span className="text-xs text-red-600">Document needs corrections</span>
                </Button>
              </div>
            </div>
          )}

          {/* Approval Form */}
          {action === 'approve' && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-green-700">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">Approving Document</span>
              </div>
              <p className="text-sm text-gray-600">
                Are you sure you want to approve this document? This action will mark the document as approved.
              </p>
            </div>
          )}

          {/* Rejection Form */}
          {action === 'reject' && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-red-700">
                <XCircle className="h-5 w-5" />
                <span className="font-medium">Rejecting Document</span>
              </div>
              <div className="space-y-2">
                <Label htmlFor="rejection-reason">Rejection Reason *</Label>
                <Textarea
                  id="rejection-reason"
                  placeholder="Explain why the document is being rejected (required)..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={3}
                  disabled={isProcessing}
                  className="border-red-200 focus:border-red-400"
                />
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
              className="flex-1"
            >
              Cancel
            </Button>
            
            {action === 'approve' && (
              <Button
                onClick={handleApprove}
                disabled={isProcessing}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                {isProcessing ? 'Approving...' : 'Approve Document'}
              </Button>
            )}
            
            {action === 'reject' && (
              <Button
                onClick={handleReject}
                disabled={isProcessing || !rejectionReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                {isProcessing ? 'Rejecting...' : 'Reject Document'}
              </Button>
            )}
            

          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
