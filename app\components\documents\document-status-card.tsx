'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  User,
  Eye,
  PenTool,
  Download,
  Truck,
  XCircle,
  CheckCircle,
} from 'lucide-react';
import { Document } from '@/types/documents';
import {
  DocumentStatusMessage,
  DocumentStatusBadge,
} from '@/components/documents/document-status-badge';

interface DocumentStatusCardProps {
  document: Document;
  isDownloadingPdf?: boolean;
  isResubmitting?: boolean; // Loading state for resubmit action
  onReview?: () => void;
  onSign?: () => void;
  onDownload?: () => void;
  onPreview?: () => void;
  onApprove?: () => void;
  onReject?: () => void;
  onResubmit?: () => void; // For resubmitting rejected documents
  className?: string;
}

export function DocumentStatusCard({
  document,
  onReview,
  onSign,
  onDownload,
  onPreview,
  onApprove,
  onReject,
  onResubmit,
  isDownloadingPdf = false,
  isResubmitting = false,
  className = '',
}: DocumentStatusCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAvailableActions = () => {
    const actions = [];

    // Preview is always available
    if (onPreview) {
      actions.push(
        <Button
          key='preview'
          variant='outline'
          size='sm'
          onClick={onPreview}
          className='flex items-center gap-2'
        >
          <Eye className='h-4 w-4' />
          Preview
        </Button>
      );
    }

    // Download is available for most statuses
    if (onDownload && document.status !== 'draft') {
      actions.push(
        <Button
          key='download'
          disabled={isDownloadingPdf}
          variant='outline'
          size='sm'
          onClick={onDownload}
          className='flex items-center gap-2'
        >
          <Download className='h-4 w-4' />
          Download
        </Button>
      );
    }

    // Review action
    if (
      onReview &&
      (document.status === 'ready_for_review' || document.status === 'draft')
    ) {
      actions.push(
        <Button
          key='review'
          variant='default'
          size='sm'
          onClick={onReview}
          className='flex items-center gap-2'
        >
          <Eye className='h-4 w-4' />
          Review
        </Button>
      );
    }

    // Sign action
    if (onSign && document.status === 'ready_for_signing') {
      actions.push(
        <Button
          key='sign'
          variant='default'
          size='sm'
          onClick={onSign}
          className='flex items-center gap-2 bg-green-600 hover:bg-green-700'
        >
          <PenTool className='h-4 w-4' />
          Sign
        </Button>
      );
    }

    // Welon Trust Review Actions - for shipped documents that need review
    if (document.status === 'shipped' && (onApprove || onReject)) {
      if (onApprove) {
        actions.push(
          <Button
            key='approve'
            variant='default'
            size='sm'
            onClick={onApprove}
            className='flex items-center gap-2 bg-green-600 hover:bg-green-700'
          >
            <CheckCircle className='h-4 w-4' />
            Approve
          </Button>
        );
      }

      if (onReject) {
        actions.push(
          <Button
            key='reject'
            variant='outline'
            size='sm'
            onClick={onReject}
            className='flex items-center gap-2 border-red-300 text-red-700 hover:bg-red-50'
          >
            <XCircle className='h-4 w-4' />
            Reject
          </Button>
        );
      }
    }

    // Resubmit action for rejected documents
    if (onResubmit && document.status === 'rejected') {
      actions.push(
        <Button
          key='resubmit'
          variant='default'
          size='sm'
          onClick={onResubmit}
          disabled={isResubmitting}
          className='flex items-center gap-2 bg-blue-600 hover:bg-blue-700'
        >
          <FileText className='h-4 w-4' />
          {isResubmitting ? 'Resubmitting...' : 'Resubmit'}
        </Button>
      );
    }

    return actions;
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className='pb-3'>
        <div className='flex items-start justify-between'>
          <div className='flex items-start gap-3'>
            <div className='p-2 bg-blue-50 rounded-lg'>
              <FileText className='h-5 w-5 text-blue-600' />
            </div>
            <div>
              <CardTitle className='text-lg'>{document.title}</CardTitle>
              <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>{document.type}</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>v{document.version}</span>
                </div>
              </div>
            </div>
          </div>
          <DocumentStatusBadge status={document.status} />
        </div>
      </CardHeader>
      <CardContent className='pt-0'>
        <div className='space-y-4'>
          {/* Status Message */}
          <DocumentStatusMessage status={document.status} />

          {/* Additional Status Info */}
          {document.status === 'shipped' && document.trackingNumber && (
            <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
              <div className='flex items-center gap-2 text-sm'>
                <Truck className='h-4 w-4 text-blue-600' />
                <span className='font-medium'>Tracking Number:</span>
                <span className='font-mono'>{document.trackingNumber}</span>
              </div>
            </div>
          )}

          {document.status === 'rejected' && document.rejectionReason && (
            <div className='bg-red-50 border border-red-200 rounded-lg p-3'>
              <div className='flex items-start gap-2 text-sm'>
                <XCircle className='h-4 w-4 text-red-600 mt-0.5' />
                <div>
                  <span className='font-medium text-red-800'>
                    Rejection Reason:
                  </span>
                  <p className='text-red-700 mt-1'>
                    {document.rejectionReason}
                  </p>
                </div>
              </div>
            </div>
          )}



          {/* Document Metadata */}
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-muted-foreground'>Created:</span>
              <div className='font-medium'>
                {formatDate(document.dateCreated)}
              </div>
            </div>
            <div>
              <span className='text-muted-foreground'>Last Modified:</span>
              <div className='font-medium'>
                {formatDate(document.lastModified)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className='flex items-center gap-2 pt-2 border-t'>
            {getAvailableActions()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
