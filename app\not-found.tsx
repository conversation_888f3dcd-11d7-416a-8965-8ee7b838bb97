'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <div className='bg-gradient-to-b from-slate-50 to-white flex items-center justify-center p-4'>
      <div className='container mx-auto max-w-4xl'>
        <div className='text-center mb-8'>
          <div className='mb-6'>
            <div className='flex justify-center mb-6'>
              <div className='w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center'>
                <AlertTriangle className='h-10 w-10 text-orange-600' />
              </div>
            </div>
          </div>

          {/* Main Error Message */}
          <h2 className='text-3xl md:text-4xl font-bold text-[var(--slate-dark)] mb-4'>
            Page Not Found
          </h2>
        </div>
        <div className={'flex justify-center w-full'}>
          <Button onClick={handleGoBack} variant='default'>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
}
