'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Plus, Eye, CheckCircle, XCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { useEvidenceSubmissions } from '@/hooks/useEvidenceSubmissions';
import { toast } from 'sonner';

// Define the EvidenceSubmission type
interface EvidenceSubmission {
  id: string;
  submitterName: string;
  submitterEmail: string;
  memberName: string;
  memberEmail: string;
  relationship: string;
  evidenceType: 'Death' | 'Incapacitation';
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  additionalInfo?: string;
  reviewedBy?: string;
  reviewedAt?: string;
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

interface EvidenceManagementTableProps {
  onView?: (evidence: EvidenceSubmission) => void;
  onApprove?: (evidence: EvidenceSubmission) => void;
  onReject?: (evidence: EvidenceSubmission) => void;
  typeParam?: 'Death' | 'Incapacitation' | null;
  compact?: boolean;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'memberName',
  searchPlaceholder: 'Filter evidence...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'pending', label: 'Pending' },
        { value: 'under_review', label: 'Under Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'rejected', label: 'Rejected' },
      ],
    },
    {
      id: 'evidenceType',
      title: 'Type',
      options: [
        { value: 'Death', label: 'Death' },
        { value: 'Incapacitation', label: 'Incapacitation' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  defaultPageSize: 10,
};

export function EvidenceManagementTable({
  onView,
  onApprove,
  onReject,
  compact = false,
  className = '',
}: EvidenceManagementTableProps) {
  // Use the custom hook to fetch and manage evidence data
  const { evidenceSubmissions, loading, error, updateEvidenceStatus } =
    useEvidenceSubmissions();

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle evidence actions
  const handleApprove = async (evidence: EvidenceSubmission) => {
    try {
      await updateEvidenceStatus(evidence.id, 'approved');
      toast.success('Evidence approved successfully');
      if (onApprove) onApprove(evidence);
    } catch (err) {
      console.error('Failed to approve evidence:', err);
      toast.error('Failed to approve evidence');
    }
  };

  const handleReject = async (evidence: EvidenceSubmission) => {
    try {
      await updateEvidenceStatus(evidence.id, 'rejected');
      toast.success('Evidence rejected successfully');
      if (onReject) onReject(evidence);
    } catch (err) {
      console.error('Failed to reject evidence:', err);
      toast.error('Failed to reject evidence');
    }
  };

  // Define columns for the data table
  const columns: ColumnDef<EvidenceSubmission>[] = [
    {
      accessorKey: 'memberName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Member' />
      ),
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='font-medium'>{row.getValue('memberName')}</span>
          <span className='text-xs text-muted-foreground'>
            {row.original.memberEmail}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'submitterName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Submitter' />
      ),
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span>{row.getValue('submitterName')}</span>
          <span className='text-xs text-muted-foreground'>
            {row.original.submitterEmail}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'relationship',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Relationship' />
      ),
      cell: ({ row }) => <span>{row.getValue('relationship')}</span>,
    },
    {
      accessorKey: 'evidenceType',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Type' />
      ),
      cell: ({ row }) => {
        const evidenceType = row.getValue('evidenceType') as string;
        return (
          <Badge variant={evidenceType === 'Death' ? 'destructive' : 'default'}>
            {evidenceType}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        let badgeVariant: 'outline' | 'default' | 'secondary' | 'destructive' =
          'outline';

        switch (status) {
          case 'approved':
            badgeVariant = 'default';
            break;
          case 'rejected':
            badgeVariant = 'destructive';
            break;
          case 'under_review':
            badgeVariant = 'secondary';
            break;
          default:
            badgeVariant = 'outline';
        }

        return (
          <Badge variant={badgeVariant} className='capitalize'>
            {status.replace('_', ' ')}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Submitted' />
      ),
      cell: ({ row }) => formatDate(row.getValue('createdAt')),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const evidence = row.original;
        const isPending = evidence.status === 'pending';

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={() => onView && onView(evidence)}>
                <Eye className='mr-2 h-4 w-4' />
                View Details
              </DropdownMenuItem>

              {isPending && (
                <>
                  <DropdownMenuItem
                    onClick={() => handleApprove(evidence)}
                    className='text-green-600'
                  >
                    <CheckCircle className='mr-2 h-4 w-4' />
                    Approve
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => handleReject(evidence)}
                    className='text-red-600'
                  >
                    <XCircle className='mr-2 h-4 w-4' />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>
            Evidence Management
          </h2>
        </div>
      </div>

      {/* Universal Data Table */}
      <DataTable
        columns={columns}
        data={evidenceSubmissions}
        config={tableConfig}
        loading={loading}
        error={error}
      />
    </div>
  );
}
