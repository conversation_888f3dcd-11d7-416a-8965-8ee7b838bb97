Welon Trust Documents

Welon Trust Document Handling Workflow After Receiving a Signed Document

Document Reception

Welon Trust receives a physically or electronically signed document from a member.

The document is recorded in the system with a timestamp.

The document status is updated to “Received by <PERSON><PERSON>” or a similar label.

Document Review

A legal or operational Welon Trust staff member reviews the document to ensure:

It meets all requirements (all signatures present, data accuracy, correct formatting).

There are no errors, corrections, or visible damage.

If issues are found, the document is returned to the member with a reason and instructions for correction (status: “Rejected”).

Approval and Upload

If the document meets all requirements:

Its status is changed to “<PERSON>lon Approved.”

The original or scanned document is uploaded into the system for storage.

The template version used to create the document is recorded for auditing and version history.

Storage and Audit

The document is stored in a secure location (e.g., Amazon S3) with proper access policies and encryption.

All actions (receipt, review, approval, rejection) are logged with staff identity, timestamps, and comments.

The document is accessible to administrators, the member, and for compliance audits.

Member Notification

The member receives a notification (email or in-app) about the result of the review:

If approved — a message confirming successful completion.

If rejected — a message with detailed instructions for next steps.

Archiving

After all steps are completed, the document status is updated to either “Current” or “Archived” depending on whether future updates occur.

 

Дії Welon Trust, якщо документ містить помилки
1. Перевірка та ідентифікація помилок
Після отримання підписаного документа співробітник Welon Trust проводить детальну перевірку на наявність помилок, невідповідностей чи відсутніх підписів.

Якщо виявлено помилки, документ не затверджується і не переходить до наступного етапу.

2. Можливості роботи з документом
Завантаження документа:
Співробітник Welon Trust має право завантажити (скачати) отриманий документ для детального аналізу, перевірки або збереження копії для аудиту.

Додавання нотаток:
У системі передбачена можливість залишати нотатки або коментарі до документа. Це можуть бути:

Опис виявлених помилок або невідповідностей.

Рекомендації щодо виправлення.

Вказівка на відсутні підписи чи неправильне оформлення.

Всі нотатки зберігаються в історії документа для прозорості та аудиту.

3. Подальші дії
Відхилення документа:
Документ отримує статус “Відхилено”. Користувачу надсилається сповіщення із зазначенням причини відхилення та інструкціями щодо виправлення помилок.

Зберігання історії:
Всі дії співробітника (завантаження, додавання нотаток, відхилення) логуються із зазначенням часу, відповідального і змісту нотаток.

Повторна подача:
Після виправлення помилок член може повторно подати документ на перевірку Welon Trust.


